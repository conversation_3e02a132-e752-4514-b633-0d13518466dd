using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Middleware;

public class EkbApiCookieMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ITokenService _tokenService;
    private readonly IConfiguration _configuration;

    public EkbApiCookieMiddleware(RequestDelegate next, ITokenService tokenService, IConfiguration configuration)
    {
        _next = next;
        _tokenService = tokenService;
        _configuration = configuration;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var EkbApiToken = await _tokenService.GetValidAccessTokenAsync();
            var ekbUrl = _configuration["ExternalApps:EkbApi:BaseUrl"]?.TrimEnd('/');

            var cookieOptions = new CookieOptions
            {
                HttpOnly = false,
                Secure = context.Request.IsHttps,
                SameSite = SameSiteMode.Lax,
                Expires = DateTimeOffset.UtcNow.AddHours(8), // Match authentication cookie duration
                Path = "/"
            };

            context.Response.Cookies.Append("EkbApiToken", EkbApiToken, cookieOptions);
            context.Response.Cookies.Append("ekbUrl", ekbUrl, cookieOptions);
        }

        await _next(context);
    }
}