{"App": {"AppName": "Imip.<PERSON>", "SelfUrl": "http://localhost:5000", "IntranetUrl": "http://**********:5000", "HealthCheckUrl": "/health-status", "IntranetMode": true, "UseHttps": false}, "ConnectionStrings": {"Default": "Server=127.0.0.1;Database=JETTY_APPROVAL;User ID=sa;Password=*********;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;Command Timeout=120;"}, "Database": {"CommandTimeout": 120, "EnableRetryOnFailure": true, "MaxRetryCount": 3, "MaxRetryDelay": 5, "EnableQuerySplitting": true}, "Seq": {"ServerUrl": "http://localhost:5341", "ApiKey": "2FafZip5QIvfaUzxx0QZ"}, "Redis": {"IsEnabled": "true", "Configuration": "localhost:6379"}, "AuthServer": {"Authority": "https://identity.imip.co.id", "RequireHttpsMetadata": false, "ClientId": "JettyApprovalLocal", "ClientSecret": "eJOOQKnfxK88qRkCkiBEhXkUTvMD94mD", "CertificatePassPhrase": "401340ab-ea38-4a9d-bce5-46e3d3e6b137"}, "OpenIdConnect": {"Authority": "https://identity.imip.co.id", "ClientId": "JettyApprovalLocal", "ClientSecret": "eJOOQKnfxK88qRkCkiBEhXkUTvMD94mD", "RequireHttpsMetadata": false, "ResponseType": "code", "UsePkce": true, "SaveTokens": true, "GetClaimsFromUserInfoEndpoint": true, "Scopes": ["openid", "profile", "email", "offline_access"], "PostLogoutRedirectUri": "http://localhost:5000/signout-callback-oidc", "SignedOutRedirectUri": "http://localhost:5000", "RedirectUri": "http://localhost:5000/signin-oidc"}, "ExternalAuth": {"ApiUrl": "http://***************/api/common/RequestAuthenticationToken", "Enabled": true}, "ExternalApps": {"IdentityServer": {"BaseUrl": "https://identity.imip.co.id/"}, "EKB": {"BaseUrl": "https://ekb-dev.imip.co.id", "InternalIp": "**********", "TimeoutSeconds": 30}, "EkbApi": {"BaseUrl": "https://ekb-dev.imip.co.id/"}, "OtherApp": {"BaseUrl": "https://example.com", "TimeoutSeconds": 30}}, "StringEncryption": {"DefaultPassPhrase": "5uOIYyxxaxv32bzK"}, "BlobStoring": {"Default": {"Type": "SFTP", "SFTP": {"Host": "**********", "Port": 22, "UserName": "ekbdev", "Password": "ekbdev#2024", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "BaseDirectory": "/ekb", "ConnectionTimeout": 30000, "OperationTimeout": 60000, "BufferSize": 4096, "CreateDirectoryIfNotExists": true}}}, "Syncfusion": {"LicenseKey": "Ngo9BigBOggjHTQxAR8/V1JEaF5cXmRCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXheeHZVRWBeVkxxV0tWYEk="}}